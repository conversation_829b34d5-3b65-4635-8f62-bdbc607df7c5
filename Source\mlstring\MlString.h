#pragma once

// Include Windows SDK compatibility header
#include "../systeminc/sdk_compatibility.h"

#include <atlstr.h>

#define ML_STRING(id,text) MultiLang::ml_string(id)
#define ML_RESOURCE(id) MultiLang::ml_string(id)

namespace MultiLang
{
  extern LCID  LocaleId ;
  extern const CString ml_string ( int StringId ) ;
  //extern const wchar_t*  SupportedCultures[] ;
  //extern const int     NumberOfCultures ;
}

