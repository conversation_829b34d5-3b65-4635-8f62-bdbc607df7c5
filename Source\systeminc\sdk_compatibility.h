#ifndef SDK_COMPATIBILITY_H
#define SDK_COMPATIBILITY_H

// Windows SDK compatibility header for newer SDK versions
// This header should be included before any Windows headers

// Set Windows version to ensure compatibility with older code
#ifndef WINVER
#define WINVER 0x0601  // Windows 7
#endif
#ifndef _WIN32_WINNT
#define _WIN32_WINNT 0x0601  // Windows 7
#endif

// Define POINTER_64 before including any Windows headers
#ifndef POINTER_64
#if defined(_WIN64)
#define POINTER_64 
#else
#define POINTER_64 __ptr64
#endif
#endif

// Define PVOID64 if not already defined
#ifndef PVOID64
typedef void * POINTER_64 PVOID64;
#endif

#endif // SDK_COMPATIBILITY_H
